import { z } from 'zod';

// ========================================
// API定义相关类型
// ========================================

export interface ApiDefinition {
  _id?: string;
  source: string;           // 数据源名称 (如: odaily, chaincatcher, foresightnews)
  endpoint: string;         // 端点名称 (如: flash, news, post, depth)
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;             // 完整的API URL
  headers: Record<string, string>;  // 请求头
  params?: Record<string, any>;     // URL参数
  body?: Record<string, any> | string | null;  // 请求体
  response_example: Record<string, any>;  // 响应示例
  metadata: {
    description?: string;   // 描述
    data_type?: string;    // 数据类型 (flash, news, article, price, etc.)
    file_path: string;     // 源文件路径
    parsed_at: Date;       // 解析时间
  };
  created_at: Date;
  updated_at: Date;
}

// ========================================
// 抓取数据相关类型
// ========================================

export interface ScrapedData {
  _id?: string;
  source: string;          // 数据源
  data_type: string;       // 数据类型
  endpoint: string;        // 端点名称
  data: Record<string, any>;  // 实际抓取的数据
  scraped_at: Date;        // 抓取时间
  metadata: {
    api_definition_id?: string;  // 关联的API定义ID
    status: 'success' | 'error'; // 抓取状态
    error_message?: string;      // 错误信息
    response_time?: number;      // 响应时间(ms)
    data_count?: number;         // 数据条数
  };
}

// ========================================
// 文件解析相关类型
// ========================================

export interface ParsedApiFile {
  file_path: string;
  source: string;
  apis: ParsedApiInfo[];
  parse_errors: string[];
}

export interface ParsedApiInfo {
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  url: string;
  headers: Record<string, string>;
  params?: Record<string, any>;
  body?: Record<string, any> | string | null;
  response_example?: Record<string, any>;
  description?: string;
  data_type?: string;
}

// ========================================
// 抓取配置类型
// ========================================

export interface ScraperConfig {
  mongodb: {
    uri: string;
    database: string;
    collections: {
      api_definitions: string;
      scraped_data: string;
    };
  };
  scraping: {
    concurrent_limit: number;    // 并发限制
    retry_attempts: number;      // 重试次数
    retry_delay: number;         // 重试延迟(ms)
    timeout: number;             // 请求超时(ms)
    rate_limit: {
      requests_per_minute: number;
      per_source: boolean;
    };
  };
  parsing: {
    draft_directory: string;     // draft目录路径
    file_extensions: string[];   // 支持的文件扩展名
    exclude_patterns: string[];  // 排除的文件模式
  };
}

// ========================================
// 统计信息类型
// ========================================

export interface ScraperStats {
  total_apis: number;
  total_sources: number;
  successful_scrapes: number;
  failed_scrapes: number;
  last_scrape_time?: Date;
  scrape_duration?: number;
  source_stats: Record<string, {
    total_apis: number;
    successful_scrapes: number;
    failed_scrapes: number;
    last_scrape_time?: Date;
    average_response_time?: number;
  }>;
}

// ========================================
// Zod验证Schema
// ========================================

export const ApiDefinitionSchema = z.object({
  source: z.string().min(1),
  endpoint: z.string().min(1),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE']),
  url: z.string().url(),
  headers: z.record(z.string()),
  params: z.record(z.any()).optional(),
  body: z.union([z.record(z.any()), z.string(), z.null()]).optional(),
  response_example: z.record(z.any()),
  metadata: z.object({
    description: z.string().optional(),
    data_type: z.string().optional(),
    file_path: z.string(),
    parsed_at: z.date(),
  }),
  created_at: z.date(),
  updated_at: z.date(),
});

export const ScrapedDataSchema = z.object({
  source: z.string().min(1),
  data_type: z.string().min(1),
  endpoint: z.string().min(1),
  data: z.record(z.any()),
  scraped_at: z.date(),
  metadata: z.object({
    api_definition_id: z.string().optional(),
    status: z.enum(['success', 'error']),
    error_message: z.string().optional(),
    response_time: z.number().optional(),
    data_count: z.number().optional(),
  }),
});

export const ScraperConfigSchema = z.object({
  mongodb: z.object({
    uri: z.string(),
    database: z.string(),
    collections: z.object({
      api_definitions: z.string(),
      scraped_data: z.string(),
    }),
  }),
  scraping: z.object({
    concurrent_limit: z.number().min(1).max(20),
    retry_attempts: z.number().min(0).max(10),
    retry_delay: z.number().min(100),
    timeout: z.number().min(1000),
    rate_limit: z.object({
      requests_per_minute: z.number().min(1),
      per_source: z.boolean(),
    }),
  }),
  parsing: z.object({
    draft_directory: z.string(),
    file_extensions: z.array(z.string()),
    exclude_patterns: z.array(z.string()),
  }),
});

// ========================================
// 查询参数类型
// ========================================

export const ApiDefinitionQuerySchema = z.object({
  source: z.string().optional(),
  endpoint: z.string().optional(),
  data_type: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sort_by: z.enum(['created_at', 'updated_at', 'source', 'endpoint']).default('created_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
});

export const ScrapedDataQuerySchema = z.object({
  source: z.string().optional(),
  data_type: z.string().optional(),
  endpoint: z.string().optional(),
  status: z.enum(['success', 'error']).optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sort_by: z.enum(['scraped_at', 'source', 'data_type']).default('scraped_at'),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
});

// ========================================
// 导出类型别名
// ========================================

export type ApiDefinitionQuery = z.infer<typeof ApiDefinitionQuerySchema>;
export type ScrapedDataQuery = z.infer<typeof ScrapedDataQuerySchema>;
export type ValidatedApiDefinition = z.infer<typeof ApiDefinitionSchema>;
export type ValidatedScrapedData = z.infer<typeof ScrapedDataSchema>;
export type ValidatedScraperConfig = z.infer<typeof ScraperConfigSchema>;
