import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../components/common/ThemeProvider';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS, SPRING_CONFIG } from '../../constants';
import TouchableScale from '../../components/ui/TouchableScale';
import PullToRefresh from '../../components/ui/PullToRefresh';
import PageHeader from '../../components/ui/PageHeader';
import { apiService } from '../../services/api';

interface NewsItem {
  id: string;
  title: string;
  time: string;
  source: string;
  isNew?: boolean;
  content?: string;
  isExpanded?: boolean;
  category?: string;
  importance?: 'low' | 'medium' | 'high';
}

const NewsScreen: React.FC = () => {
  const { colors } = useTheme();
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newItemsCount, setNewItemsCount] = useState(0);
  const [showNewItemsBanner, setShowNewItemsBanner] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const bannerAnim = useRef(new Animated.Value(0)).current;
  const styles = createStyles(colors);

  // 模拟新闻数据
  const mockNewsItems: NewsItem[] = [
    {
      id: '1',
      title: 'Bitcoin突破$50,000大关，创历史新高',
      time: '2分钟前',
      source: 'CoinDesk',
      category: '加密货币',
      importance: 'high',
      content: 'Bitcoin价格在今日早间突破$50,000重要心理关口，创下历史新高。这一突破主要受到机构投资者持续买入和市场情绪改善的推动。分析师认为，这标志着加密货币市场进入新的牛市阶段。',
      isExpanded: false,
    },
    {
      id: '2',
      title: 'Ethereum 2.0质押量突破3200万ETH',
      time: '5分钟前',
      source: 'The Block',
      category: '技术',
      importance: 'medium',
      content: 'Ethereum 2.0信标链的质押量已突破3200万ETH，约占ETH总供应量的26%。这一里程碑表明了社区对以太坊向权益证明机制转换的强烈信心。质押收益率目前维持在4.2%左右。',
      isExpanded: false,
    },
    {
      id: '3',
      title: 'DeFi协议总锁仓价值达到1000亿美元',
      time: '8分钟前',
      source: 'DeFi Pulse',
      category: 'DeFi',
      importance: 'high',
      content: '去中心化金融(DeFi)生态系统的总锁仓价值(TVL)首次突破1000亿美元大关。Uniswap、Aave和Compound等主要协议贡献了大部分锁仓价值。这一成就标志着DeFi已成为传统金融的重要补充。',
      isExpanded: false,
    },
    {
      id: '4',
      title: 'NFT市场交易量创月度新高',
      time: '12分钟前',
      source: 'OpenSea',
      category: 'NFT',
      importance: 'medium',
      content: 'NFT市场在本月的交易量达到创纪录的45亿美元，超过了此前的历史高点。艺术品、游戏道具和虚拟土地是最受欢迎的NFT类别。主要交易平台OpenSea的月活跃用户数也创下新高。',
      isExpanded: false,
    },
    {
      id: '5',
      title: '美国SEC发布加密货币监管新指导意见',
      time: '15分钟前',
      source: 'Reuters',
      category: '监管',
      importance: 'high',
      content: '美国证券交易委员会(SEC)发布了关于加密货币监管的最新指导意见，明确了数字资产的分类标准和合规要求。新规定将为加密货币交易所和项目方提供更清晰的监管框架，有助于行业的健康发展。',
      isExpanded: false,
    },
  ];

  useEffect(() => {
    loadFlashNews();

    // 设置自动刷新定时器
    const interval = setInterval(() => {
      checkForNewItems();
    }, 30000); // 30秒检查一次

    return () => clearInterval(interval);
  }, []);

  // 加载快讯数据
  const loadFlashNews = async (page = 1, isRefresh = false) => {
    try {
      if (page === 1) {
        setLoading(true);
      }

      const response = await apiService.getFlashNews(page, 20);

      if (response.success) {
        const transformedItems: NewsItem[] = response.data.items.map((item: any) => ({
          id: item.id,
          title: item.title,
          time: formatTime(item.publishedAt),
          source: item.source?.name || 'Unknown',
          content: item.summary || item.content,
          category: item.category || '快讯',
          importance: item.isImportant ? 'high' : (item.isHot ? 'medium' : 'low'),
          isExpanded: false,
        }));

        if (isRefresh || page === 1) {
          setNewsItems(transformedItems);
          setCurrentPage(1);
        } else {
          setNewsItems(prev => [...prev, ...transformedItems]);
        }

        setCurrentPage(page);
        setHasMore(response.data.hasMore);
      }
    } catch (error) {
      console.error('加载快讯失败:', error);
      // 如果API失败，使用模拟数据
      if (page === 1) {
        setNewsItems(mockNewsItems);
      }
    } finally {
      setLoading(false);
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
    });
  };

  useEffect(() => {
    if (showNewItemsBanner) {
      Animated.spring(bannerAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    } else {
      Animated.spring(bannerAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: SPRING_CONFIG.tension,
        friction: SPRING_CONFIG.friction,
      }).start();
    }
  }, [showNewItemsBanner]);

  const checkForNewItems = async () => {
    try {
      // 检查是否有新的快讯
      const response = await apiService.getFlashNews(1, 5);
      if (response.success && response.data.items.length > 0) {
        const latestItem = response.data.items[0];
        const currentLatestItem = newsItems[0];

        // 如果最新的快讯ID不同，说明有新内容
        if (currentLatestItem && latestItem.id !== currentLatestItem.id) {
          setNewItemsCount(1);
          setShowNewItemsBanner(true);
        }
      }
    } catch (error) {
      console.error('检查新内容失败:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    try {
      await loadFlashNews(1, true);

      // 隐藏新内容横幅
      setShowNewItemsBanner(false);
      setNewItemsCount(0);
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const loadMoreItems = async () => {
    if (!hasMore || loading) return;

    await loadFlashNews(currentPage + 1, false);
  };

  const handleNewItemsBannerPress = () => {
    handleRefresh();
  };

  const handleNewsItemPress = (itemId: string) => {
    setNewsItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId
          ? { ...item, isExpanded: !item.isExpanded }
          : item
      )
    );
  };

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case '加密货币': return colors.warning;
      case 'DeFi': return colors.primary;
      case 'NFT': return colors.secondary;
      case '技术': return colors.success;
      case '监管': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getImportanceIcon = (importance?: string) => {
    switch (importance) {
      case 'high': return 'flash';
      case 'medium': return 'trending-up';
      case 'low': return 'information-circle';
      default: return 'information-circle';
    }
  };

  const renderNewsItem = ({ item, index }: { item: NewsItem; index: number }) => (
    <View style={styles.newsItemWrapper}>
      <View
        style={styles.newsItem}
      >
        <View style={styles.timelineContainer}>
          <View style={[
            styles.timelineDot,
            item.isNew && styles.newTimelineDot,
            item.importance === 'high' && styles.highImportanceDot
          ]}>
            {item.importance === 'high' && (
              <Ionicons
                name={getImportanceIcon(item.importance)}
                size={8}
                color="#FFFFFF"
              />
            )}
          </View>
        </View>

        <TouchableOpacity
          style={styles.newsContentContainer}
          onPress={() => handleNewsItemPress(item.id)}
        >
          <View style={styles.newsContent}>
            <View style={styles.newsHeader}>
              <View style={styles.newsTitleContainer}>
                {item.category && (
                  <View style={[styles.categoryTag, { backgroundColor: getCategoryColor(item.category) + '20' }]}>
                    <Text style={[styles.categoryText, { color: getCategoryColor(item.category) }]}>
                      {item.category}
                    </Text>
                  </View>
                )}
                <Text style={[styles.newsTitle, item.isNew && styles.newNewsTitle]} numberOfLines={item.isExpanded ? undefined : 2}>
                  {item.title}
                </Text>
              </View>
              <Ionicons
                name={item.isExpanded ? "chevron-up" : "chevron-down"}
                size={16}
                color={colors.textSecondary}
                style={styles.expandIcon}
              />
            </View>

            {item.isExpanded && item.content && (
              <View style={styles.expandedContent}>
                <Text style={styles.newsContentText}>
                  {item.content}
                </Text>
              </View>
            )}

            <View style={styles.newsMeta}>
              <View style={styles.newsMetaLeft}>
                <Text style={styles.newsTime}>{item.time}</Text>
                <Text style={styles.newsSource}>• {item.source}</Text>
              </View>
              {item.isExpanded && (
                <TouchableOpacity style={styles.shareButton}>
                  <Ionicons name="share-outline" size={14} color={colors.primary} />
                  <Text style={styles.shareText}>分享</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </TouchableOpacity>

        {item.isNew && (
          <View style={styles.newBadge}>
            <Text style={styles.newBadgeText}>新</Text>
          </View>
        )}
      </View>

      {/* 连续的时间线 */}
      {index < newsItems.length && (
        <View style={styles.timelineLine} />
      )}
    </View>
  );

  const renderNewItemsBanner = () => (
    <Animated.View
      style={[
        styles.newItemsBanner,
        {
          opacity: bannerAnim,
          transform: [
            {
              translateY: bannerAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-50, 0],
              }),
            },
          ],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.bannerContent}
        onPress={handleNewItemsBannerPress}
        activeOpacity={0.8}
      >
        <Ionicons name="arrow-up-circle" size={20} color={colors.primary} />
        <Text style={styles.bannerText}>
          有 {newItemsCount} 条新快讯
        </Text>
        <Text style={styles.bannerAction}>点击查看</Text>
      </TouchableOpacity>
    </Animated.View>
  );

  const renderLoadingFooter = () => (
    <View style={styles.loadingFooter}>
      <Text style={styles.loadingText}>加载中...</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <PageHeader
        title="快讯"
        rightComponent={
          <TouchableScale style={styles.refreshButton} onPress={handleRefresh}>
            <Ionicons name="refresh" size={20} color={colors.text} />
          </TouchableScale>
        }
      />

      {showNewItemsBanner && renderNewItemsBanner()}

      <FlatList
        data={newsItems}
        renderItem={renderNewsItem}
        keyExtractor={(item) => item.id}
        style={styles.newsList}
        contentContainerStyle={styles.newsListContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <PullToRefresh refreshing={refreshing} onRefresh={handleRefresh} />
        }
        onEndReached={loadMoreItems}
        onEndReachedThreshold={0.1}
        ListFooterComponent={loading && hasMore ? renderLoadingFooter : null}
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    refreshButton: {
      padding: SPACING.sm,
    },
    newItemsBanner: {
      backgroundColor: colors.primary + '15',
      borderBottomWidth: 1,
      borderBottomColor: colors.primary + '30',
    },
    bannerContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
    },
    bannerText: {
      fontSize: FONT_SIZES.base,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.semibold,
      marginLeft: SPACING.sm,
      marginRight: SPACING.sm,
    },
    bannerAction: {
      fontSize: FONT_SIZES.sm,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    newsList: {
      flex: 1,
      paddingLeft: 8,
      paddingRight: 8
    },
    newsListContent: {
      paddingVertical: SPACING.sm,
    },
    newsItemWrapper: {
      position: 'relative',
    },
    newsItem: {
      flexDirection: 'row',
      paddingHorizontal: SPACING.xs, // 进一步减少水平内边距
      paddingVertical: SPACING.md,
      position: 'relative',
      minHeight: 80,
    },
    timelineContainer: {
      alignItems: 'center',
      flexDirection: 'row',
      justifyContent: 'center',
      marginRight: SPACING.sm, // 增加右边距以改善对齐
      width: 20, // 增加宽度以更好地容纳圆点
      paddingTop: 4, // 调整顶部内边距以改善对齐
      flexShrink: 0,
      position: 'relative', // 添加相对定位以更好地控制时间线
    },
    timelineDot: {
      width: 10, // 增加圆点大小
      height: 10,
      borderRadius: '100%',
      backgroundColor: colors.border,
      zIndex: 2,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2, // 添加边框
      borderColor: colors.background, // 白色边框以增强视觉效果
    },
    newTimelineDot: {
      backgroundColor: colors.primary,
      width: 12, // 增加新快讯圆点大小
      height: 12,
      borderRadius: '100%',
      shadowColor: colors.primary,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.4, // 增强阴影效果
      shadowRadius: 6,
      elevation: 3,
      borderWidth: 3, // 增加边框宽度
      borderColor: colors.background, // 白色边框
      zIndex: 2,
    },
    highImportanceDot: {
      backgroundColor: colors.error,
      width: 14, // 增加高重要性圆点大小
      height: 14,
      borderRadius: 7,
      borderWidth: 3, // 添加边框
      borderColor: colors.background, // 白色边框
      shadowColor: colors.error,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 2,
      zIndex: 2,
    },
    timelineLine: {
      position: 'absolute',
      left: SPACING.sm + 5, // 调整位置以匹配新的时间线容器设置
      top: 24, // 调整顶部位置以更好地对齐圆点
      width: 2, // 增加线条宽度
      height: '100%',
      backgroundColor: colors.border,
      zIndex: 1,
      borderRadius: 1, // 添加圆角
    },
    newsContentContainer: {
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.lg,
      marginVertical: SPACING.xs,
      overflow: 'hidden',
      marginRight: 0, // 移除右边距，让内容容器占用最大可用空间
      maxWidth: '100%', // 确保不会超出容器宽度
      flex: 1, // 确保容器占用所有可用空间
    },
    newsContent: {
      flex: 1,
      padding: SPACING.md,
      width: '100%', // 确保内容占用全部宽度
    },
    newsHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      justifyContent: 'space-between',
      marginBottom: SPACING.sm,
      width: '100%', // 确保header占用全部宽度
    },
    newsTitleContainer: {
      flex: 1,
      marginRight: SPACING.xs,
      minWidth: 0, // 确保flex子元素可以正确收缩
      // 移除width: '100%'，因为与flex: 1冲突
    },
    categoryTag: {
      alignSelf: 'flex-start',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      borderRadius: BORDER_RADIUS.sm,
      marginBottom: SPACING.xs,
    },
    categoryText: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.semibold,
    },
    expandIcon: {
      marginLeft: SPACING.sm,
      marginTop: 2,
    },
    expandedContent: {
      marginTop: SPACING.sm,
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.borderLight,
      width: '100%', // 确保展开内容占用全部宽度
    },
    newsContentText: {
      fontSize: FONT_SIZES.sm,
      color: colors.text,
      lineHeight: 22, // 增加行高以改善可读性
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.sm,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BORDER_RADIUS.md,
      borderLeftWidth: 3,
      borderLeftColor: colors.primary,
      // Text组件不支持flexWrap，移除无效属性
    },
    newsTitle: {
      fontSize: FONT_SIZES.base,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.text,
      lineHeight: 22, // 增加行高以改善可读性
      marginBottom: SPACING.sm,
      // Text组件不支持flexWrap，移除无效属性
    },
    newNewsTitle: {
      color: colors.primary,
    },
    newsMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexWrap: 'wrap',
      marginTop: SPACING.sm,
    },
    newsMetaLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    shareButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      backgroundColor: colors.primary + '20',
      borderRadius: BORDER_RADIUS.sm,
      marginLeft: SPACING.xs, // 减少左边距
      flexShrink: 0, // 防止按钮被压缩
    },
    shareText: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      fontWeight: FONT_WEIGHTS.medium,
      marginLeft: SPACING.xs,
    },
    newsTime: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    newsSource: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    newBadge: {
      backgroundColor: colors.primary,
      borderRadius: BORDER_RADIUS.sm,
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
      alignSelf: 'flex-start',
      marginLeft: SPACING.sm,
    },
    newBadgeText: {
      fontSize: FONT_SIZES.xs,
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.bold,
    },
    loadingFooter: {
      paddingVertical: SPACING.lg,
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
  });

export default NewsScreen;
