import { MongoClient, Db, Collection, CreateIndexesOptions } from 'mongodb';
import { ApiDefinition, ScrapedData, ScraperConfig, ApiDefinitionQuery, ScrapedDataQuery } from './types';
import { logger } from '../utils/logger';

export class ScraperDatabase {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private config: ScraperConfig;
  private isConnected = false;

  constructor(config: ScraperConfig) {
    this.config = config;
  }

  /**
   * 连接到MongoDB
   */
  async connect(): Promise<void> {
    try {
      if (this.isConnected && this.client) {
        return;
      }

      logger.info('🔌 连接到MongoDB...', {
        uri: this.config.mongodb.uri.replace(/\/\/.*@/, '//***:***@'), // 隐藏密码
        database: this.config.mongodb.database,
      });

      this.client = new MongoClient(this.config.mongodb.uri, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      });

      await this.client.connect();
      this.db = this.client.db(this.config.mongodb.database);
      this.isConnected = true;

      // 创建索引
      await this.createIndexes();

      logger.info('✅ MongoDB连接成功');
    } catch (error) {
      logger.error('❌ MongoDB连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.client = null;
        this.db = null;
        this.isConnected = false;
        logger.info('🔌 MongoDB连接已断开');
      }
    } catch (error) {
      logger.error('❌ MongoDB断开连接失败:', error);
      throw error;
    }
  }

  /**
   * 创建数据库索引
   */
  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('数据库未连接');

    try {
      const apiDefsCollection = this.getApiDefinitionsCollection();
      const scrapedDataCollection = this.getScrapedDataCollection();

      // API定义集合索引
      await apiDefsCollection.createIndexes([
        { key: { source: 1, endpoint: 1 }, unique: true },
        { key: { source: 1 } },
        { key: { 'metadata.data_type': 1 } },
        { key: { created_at: -1 } },
        { key: { updated_at: -1 } },
      ]);

      // 抓取数据集合索引
      await scrapedDataCollection.createIndexes([
        { key: { source: 1, data_type: 1, endpoint: 1 } },
        { key: { source: 1 } },
        { key: { data_type: 1 } },
        { key: { scraped_at: -1 } },
        { key: { 'metadata.status': 1 } },
        { key: { 'metadata.api_definition_id': 1 } },
      ]);

      logger.info('📊 数据库索引创建完成');
    } catch (error) {
      logger.error('❌ 创建索引失败:', error);
      throw error;
    }
  }

  /**
   * 获取API定义集合
   */
  private getApiDefinitionsCollection(): Collection<ApiDefinition> {
    if (!this.db) throw new Error('数据库未连接');
    return this.db.collection<ApiDefinition>(this.config.mongodb.collections.api_definitions);
  }

  /**
   * 获取抓取数据集合
   */
  private getScrapedDataCollection(): Collection<ScrapedData> {
    if (!this.db) throw new Error('数据库未连接');
    return this.db.collection<ScrapedData>(this.config.mongodb.collections.scraped_data);
  }

  // ========================================
  // API定义相关操作
  // ========================================

  /**
   * 保存API定义
   */
  async saveApiDefinition(apiDef: Omit<ApiDefinition, '_id'>): Promise<string> {
    const collection = this.getApiDefinitionsCollection();
    
    try {
      const result = await collection.replaceOne(
        { source: apiDef.source, endpoint: apiDef.endpoint },
        { ...apiDef, updated_at: new Date() },
        { upsert: true }
      );

      const id = result.upsertedId?.toString() || 
                 (await collection.findOne({ source: apiDef.source, endpoint: apiDef.endpoint }))?._id?.toString();

      if (!id) throw new Error('无法获取API定义ID');

      logger.debug('💾 API定义已保存', {
        source: apiDef.source,
        endpoint: apiDef.endpoint,
        id,
      });

      return id;
    } catch (error) {
      logger.error('❌ 保存API定义失败:', error);
      throw error;
    }
  }

  /**
   * 批量保存API定义
   */
  async saveApiDefinitions(apiDefs: Omit<ApiDefinition, '_id'>[]): Promise<string[]> {
    const collection = this.getApiDefinitionsCollection();
    const ids: string[] = [];

    try {
      for (const apiDef of apiDefs) {
        const id = await this.saveApiDefinition(apiDef);
        ids.push(id);
      }

      logger.info('💾 批量保存API定义完成', {
        count: apiDefs.length,
        sources: [...new Set(apiDefs.map(def => def.source))],
      });

      return ids;
    } catch (error) {
      logger.error('❌ 批量保存API定义失败:', error);
      throw error;
    }
  }

  /**
   * 查询API定义
   */
  async queryApiDefinitions(query: ApiDefinitionQuery) {
    const collection = this.getApiDefinitionsCollection();
    
    try {
      // 构建查询条件
      const filter: any = {};
      if (query.source) filter.source = query.source;
      if (query.endpoint) filter.endpoint = query.endpoint;
      if (query.data_type) filter['metadata.data_type'] = query.data_type;

      // 构建排序条件
      const sort: any = {};
      sort[query.sort_by] = query.sort_order === 'asc' ? 1 : -1;

      // 执行查询
      const skip = (query.page - 1) * query.limit;
      const [items, total] = await Promise.all([
        collection.find(filter).sort(sort).skip(skip).limit(query.limit).toArray(),
        collection.countDocuments(filter),
      ]);

      return {
        items,
        total,
        page: query.page,
        limit: query.limit,
        totalPages: Math.ceil(total / query.limit),
        hasMore: skip + items.length < total,
      };
    } catch (error) {
      logger.error('❌ 查询API定义失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取API定义
   */
  async getApiDefinitionById(id: string): Promise<ApiDefinition | null> {
    const collection = this.getApiDefinitionsCollection();
    
    try {
      const result = await collection.findOne({ _id: id as any });
      return result;
    } catch (error) {
      logger.error('❌ 获取API定义失败:', error);
      throw error;
    }
  }

  // ========================================
  // 抓取数据相关操作
  // ========================================

  /**
   * 保存抓取数据
   */
  async saveScrapedData(data: Omit<ScrapedData, '_id'>): Promise<string> {
    const collection = this.getScrapedDataCollection();
    
    try {
      const result = await collection.insertOne(data);
      const id = result.insertedId.toString();

      logger.debug('💾 抓取数据已保存', {
        source: data.source,
        data_type: data.data_type,
        endpoint: data.endpoint,
        status: data.metadata.status,
        id,
      });

      return id;
    } catch (error) {
      logger.error('❌ 保存抓取数据失败:', error);
      throw error;
    }
  }

  /**
   * 查询抓取数据
   */
  async queryScrapedData(query: ScrapedDataQuery) {
    const collection = this.getScrapedDataCollection();
    
    try {
      // 构建查询条件
      const filter: any = {};
      if (query.source) filter.source = query.source;
      if (query.data_type) filter.data_type = query.data_type;
      if (query.endpoint) filter.endpoint = query.endpoint;
      if (query.status) filter['metadata.status'] = query.status;
      
      if (query.date_from || query.date_to) {
        filter.scraped_at = {};
        if (query.date_from) filter.scraped_at.$gte = new Date(query.date_from);
        if (query.date_to) filter.scraped_at.$lte = new Date(query.date_to);
      }

      // 构建排序条件
      const sort: any = {};
      sort[query.sort_by] = query.sort_order === 'asc' ? 1 : -1;

      // 执行查询
      const skip = (query.page - 1) * query.limit;
      const [items, total] = await Promise.all([
        collection.find(filter).sort(sort).skip(skip).limit(query.limit).toArray(),
        collection.countDocuments(filter),
      ]);

      return {
        items,
        total,
        page: query.page,
        limit: query.limit,
        totalPages: Math.ceil(total / query.limit),
        hasMore: skip + items.length < total,
      };
    } catch (error) {
      logger.error('❌ 查询抓取数据失败:', error);
      throw error;
    }
  }

  // ========================================
  // 统计相关操作
  // ========================================

  /**
   * 获取统计信息
   */
  async getStats() {
    try {
      const apiDefsCollection = this.getApiDefinitionsCollection();
      const scrapedDataCollection = this.getScrapedDataCollection();

      const [
        totalApis,
        totalSources,
        successfulScrapes,
        failedScrapes,
        lastScrapeTime,
      ] = await Promise.all([
        apiDefsCollection.countDocuments(),
        apiDefsCollection.distinct('source').then(sources => sources.length),
        scrapedDataCollection.countDocuments({ 'metadata.status': 'success' }),
        scrapedDataCollection.countDocuments({ 'metadata.status': 'error' }),
        scrapedDataCollection.findOne({}, { sort: { scraped_at: -1 } }),
      ]);

      return {
        total_apis: totalApis,
        total_sources: totalSources,
        successful_scrapes: successfulScrapes,
        failed_scrapes: failedScrapes,
        last_scrape_time: lastScrapeTime?.scraped_at,
      };
    } catch (error) {
      logger.error('❌ 获取统计信息失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected || !this.db) {
        return false;
      }

      await this.db.admin().ping();
      return true;
    } catch (error) {
      logger.error('❌ 数据库健康检查失败:', error);
      return false;
    }
  }
}
