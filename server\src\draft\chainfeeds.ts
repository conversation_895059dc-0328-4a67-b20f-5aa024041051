const headers = {
  accept: 'application/json, text/plain, */*',
  'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'cache-control': 'no-cache',
  'd-token': 'null',
  pragma: 'no-cache',
  priority: 'u=1, i',
  'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  Referer: 'https://www.chainfeeds.xyz/',
  Origin: 'https://www.chainfeeds.xyz/',
  'sec-fetch-site': 'same-site',
  tenant: '1',
};
const getFeed = async () => {
  // 资讯
  fetch('https://api.chainfeeds.xyz/feed/list?page=1&page_size=20&group_alias=selected', {
    headers: headers,
    referrerPolicy: 'no-referrer',
    body: null,
    method: 'GET',
  });

  const respone = {
    code: 0,
    message: '成功',
    showErr: 0,
    currentTime: 1752143616,
    data: {
      top_news: [],
      total_count: 13517,
      total_page: 676,
      list: [
        {
          uuid: 'da504d2e-12e3-44af-b056-a249dcb0b4a8',
          title:
            '105 页报告解密：量化交易巨头 Jane Street 484 亿罚单背后的算法操纵，谁在市场食物链的顶端？',
          abstract: '螳螂捕蝉，谁是黄雀？',
          source_url: 'https:\/\/mp.weixin.qq.com\/s\/BPvoId7NQCR7_gCqeir_9w',
          author_name: 'AiYing Compliance',
          recommender_id: '4113',
          recommender_name: 'ChainFeeds',
          type: 1,
          is_star: 1,
          show_time: '2025-07-10T05:28:58Z',
          view_count: 19,
          banner: null,
          substack_slug: null,
          is_ads: '0',
          ads_banner: '',
          avatar_url: 'https:\/\/images.chainfeeds.xyz\/image\/685ac25586254.png',
          author_title: '',
          author_twitter: 'AiYing Compliance',
          author_twitter_description: '',
          recommender_info: {
            id: '4113',
            nickname: 'ChainFeeds',
            title: '',
            avatar_url: 'https:\/\/images.chainfeeds.xyz\/image\/GxRYCgD8_400x400.jpg',
            twitter: 'https:\/\/x.com\/ChainFeedsxyz',
            twitter_description:
              'Content aggregator for Web3. \nSubscribe to our daily Newsletter: https:\/\/t.co\/mw2cNyrWrt',
            wallet: '',
            type: '',
          },
          opinion: [
            {
              uuid: '7437c266-0774-4c86-a464-7d8fbac64ae6',
              comment:
                '要理解 Jane Street 案件的严重性，必须从其策略的结构化程度与系统性下手。SEBI 指出，这不是一次偶发事件或局部违规行为，而是一整套经过设计、跨市场、多实体配合的操纵系统，围绕两个核心策略展开：一是「日内指数操纵」，二是「收盘价操纵」。在前者中，Jane Street 利用本地实体于上午时段在流动性较弱的现货和股指期货市场主动推高关键成分股价格，制造指数上涨假象，随后通过海外 FPI 实体低成本布局大规模空头期权头寸。下午阶段，其本地实体反向大举抛售头寸，使指数快速下跌，从而使看跌期权大幅升值，实现期权市场的巨额套利。这一过程形成了一个完美的操纵闭环：现货市场「高买低卖」的亏损，被期权市场成倍的利润覆盖。而在「收盘价操纵」策略中，Jane Street 利用临近结算窗口时点，通过大额买卖订单影响指数收盘价，使得其期权头寸获益最大化。这些操作均基于跨实体协作完成，规避了 FPI 不得日内反向交易的限制，展现出极高的技术熟练度和合规边界把握能力，构成了对市场公平性的严重破坏。\n\nSEBI 的应对并非依赖追溯交易算法或程序细节，而是明确采用「原则为本」的监管范式，聚焦操纵行为对市场秩序的实际影响。其依据《禁止欺诈和不公平交易条例》（PFUTP Regulations）提出三大定性：一是制造虚假价格信号，误导市场参与者；二是操纵关键基准价格以配合衍生品头寸；三是其现货 \/ 期货市场交易缺乏独立经济合理性，即「故意亏损」以服务更高层级的期权盈利目标。SEBI 强调，技术本身中立，但其使用方式不中立。Jane Street 的策略虽未违反每一项明文规定，但整体结构违背市场基本原则，构成恶意操纵。此外，SEBI 还特别指出，该机构曾在案发前对其发出正式警告信，Jane Street 却在几个月后继续采用相似手法操控市场。这种无视监管的傲慢，直接促使 SEBI 采取了高额罚款与临时市场禁入的重罚。这不仅是对 Jane Street 的处罚，更是对所有高频量化机构的制度性警示：在全球市场的监管框架快速进化、原则导向强化的背景下，任何脱离市场公平本质的技术应用都将面临制度性风险的反噬。\n\nJane Street 印度案的手法与 Crypto 市场中的一系列操纵事件具有高度同构性，揭示了技术优势如何在缺乏监管约束的系统中演变为操控工具。无论是 Mango Markets 中通过操纵预言机抬高抵押品获取杠杆，FTX\/Alameda 的平台币伪价套利，还是 BitMEX 纵容内部员工在衍生品市场洗盘交易，这些行为本质上都在利用某种结构性漏洞制造信息不对称，从而实现对普通参与者的「系统性捕猎」。不同于传统金融对市场操纵已有成熟立法，加密领域的灰色地带更广、界限更模糊，使得类似行为更隐蔽、更常态化。Jane Street 在传统金融中所展现的策略，正是 Crypto 项目方、做市商和头部交易所长期默契运作的「隐形剧本」：通过资产价格操控、算法驱动订单动态、甚至社交媒体制造叙事影响，从而误导散户和中小机构。正因如此，Jane Street 案不仅是传统监管向量化霸权亮剑的案例，更是加密行业必须直面的镜子 —— 市场操纵并非金融中心独有，而是所有以数据和算法为载体的博弈系统的潜在共病。理解这些模式、识别其演化逻辑，是所有 Crypto 项目和参与者的合规前提与生存底线。',
              source_url: 'https:\/\/mp.weixin.qq.com\/s\/BPvoId7NQCR7_gCqeir_9w',
              author_name: 'AiYing Compliance',
              origin_author_name: 'AiYing Compliance',
              avatar_url: 'https:\/\/images.chainfeeds.xyz\/image\/685ac25586254.png',
              author_title: '',
              show_time: '2025-07-10T03:31:39Z',
              link_source: {
                name: '微信',
                link: 'https:\/\/mp.weixin.qq.com\/',
                logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/62922f7003da0.png',
                english_name: null,
                traditional_name: null,
              },
              like_users: [],
            },
          ],
          tags: [
            {
              tag_id: '8217',
              tag_alias: '',
              tag_name: 'Jane street',
            },
          ],
          link_source: {
            name: '微信',
            link: 'https:\/\/mp.weixin.qq.com\/',
            logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/62922f7003da0.png',
            english_name: null,
            traditional_name: null,
          },
          reaction: [],
          library_id_list: [],
          is_lock: false,
        },
      ],
    },
  };
};
const getFlash = async () => {
  // 精选
  fetch('https://api.chainfeeds.xyz/feed/list?page=1&page_size=20&group_alias=flash', {
    headers: headers,
    referrerPolicy: 'no-referrer',
    body: null,
    method: 'GET',
  });

  const respone = {
    code: 0,
    message: '成功',
    showErr: 0,
    currentTime: 1752143616,
    data: {
      top_news: [],
      total_count: 13517,
      total_page: 676,
      list: [
        {
          uuid: '41335f05-c8eb-4365-82e7-d900ecbd73f4',
          title: 'Binance Labs 更名为 YZi Labs，CZ 将继续在投资活动中发挥关键作用',
          abstract:
            'Binance Labs 宣布更名为 YZi Labs，并将投资重点扩展至人工智能和生物技术领域。此外，CZ 将会继续在投资活动中发挥关键作用，直接与创始人接触并提供指导和指导。同时，Ella Zhang 将回归担任 YZi Labs 负责人。\n根据公告，Ella Zhang 与 CZ 于 2018 年共同创立了 Binance Labs，并担任首席负责人，随后于 2020 年离职。',
          source_url: 'https:\/\/x.com\/BinanceLabs\/status\/1882428195267559770',
          author_name: 'ChainFeeds',
          recommender_id: '4114',
          recommender_name: 'ChainFeeds 快讯',
          type: 2,
          is_star: 1,
          show_time: '2025-01-24T01:56:40Z',
          view_count: 7,
          banner: null,
          substack_slug: null,
          is_ads: '0',
          ads_banner: '',
          avatar_url: 'https:\/\/images.chainfeeds.xyz\/image\/628dca2c7fe69.png',
          author_title: null,
          author_twitter: '',
          author_twitter_description: null,
          recommender_info: {
            id: '4114',
            nickname: 'ChainFeeds 快讯',
            title: '',
            avatar_url: 'https:\/\/images.chainfeeds.xyz\/image\/GxRYCgD8_400x400.jpg',
            twitter: 'https:\/\/twitter.com\/ChainFeedsxyz',
            twitter_description:
              'Content aggregator for Web3. \nSubscribe to our daily Newsletter: https:\/\/t.co\/mw2cNyrWrt',
            wallet: '',
            type: '',
          },
          opinion: [],
          tags: [
            {
              tag_id: '113',
              tag_alias: 'binancelabs',
              tag_name: 'Binance Labs',
            },
          ],
          link_source: {
            name: 'Twitter',
            link: 'https:\/\/twitter.com\/',
            logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/62922f78e7a79.png',
            english_name: null,
            traditional_name: null,
          },
          reaction: [],
          library_id_list: [],
          is_lock: false,
        },
      ],
    },
  };
};
const getTopic = async () => {
  // 主题新闻
  fetch('https://api.chainfeeds.xyz/theme/list?page=1&page_size=10&language=zh-cn', {
    headers: headers,
    referrerPolicy: 'no-referrer',
    body: null,
    method: 'GET',
  });

  const respone = {
    code: 0,
    message: '成功',
    showErr: 0,
    currentTime: 1752143616,
    data: {
      total_count: 6,
      total_page: 1,
      list: [
        {
          article_source_id: 20360355,
          cluster_article_id: 334668,
          source_type: '31',
          author_name: '',
          uuid: '290bbd0e-81da-4a0f-b9f9-1f1b40d1d9fe',
          title: '慢雾：GMX被盗系GLP价格被操控，攻击者通过重入创建大额空头头寸操纵全局平均价格',
          abstract:
            '<p>BlockBeats 消息，7 月 10 日，慢雾余弦发文称，「昨晚 GMX 被盗 4200 万美元的根本原因是 GMX v1 在处理空头头寸时会立即更新全局空头平均价格 (globalShortAveragePrices)，而这个全局平均价格将直接影响总资产规模 (AUM) 的计算，进而导致 GLP 代币价格被操控。<\/p><p><br><\/p><p>攻击者利用这个设计缺陷通过 Keeper 在执行订单时会启用 timelock.enableLeverage 的特性 (创建大额空单的必要条件)，通过重入的方式成功创建大额空头头寸操纵全局平均价格，以在单笔交易中人为抬高 GLP 价格并通过赎回操作获利。」<\/p>',
          status: 1,
          source_url: 'https:\/\/m.theblockbeats.info\/flash\/302016',
          url_original: 'https:\/\/m.theblockbeats.info\/flash\/302016',
          show_time: '2025-07-10T05:30:20Z',
          last_create_time: '2025-07-10T06:20:01Z',
          article_num: 7,
          publish_id: 0,
          publish_name: '',
          operator_id: 0,
          operator: '',
          create_time: '2025-07-10T05:50:31Z',
          update_time: '2025-07-10T06:20:01Z',
          weights: '1.00',
          score: '50.32',
          theme_id: 14774,
          link_source: {
            name: '律动 BlockBeats',
            link: 'https:\/\/www.theblockbeats.info\/',
            logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
            english_name: null,
            traditional_name: null,
          },
          relation: {
            article_source: [
              {
                article_source_id: '20354022',
                title: 'GMX：建议GMX V1分叉项目禁用杠杆并限制代币铸造防范风险',
                source_url: 'https:\/\/m.theblockbeats.info\/flash\/301916',
                source_type: '31',
                url_original: 'https:\/\/m.theblockbeats.info\/flash\/301916',
                show_time: '2025-07-09T14:52:46Z',
                link_source_name: '律动 BlockBeats',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '律动 BlockBeats',
                  link: 'https:\/\/www.theblockbeats.info\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20354024',
                title:
                  'GMX：已暂停Arbitrum和Avalanche上GMX V1交易以及GLP铸造和赎回，V2及GMX代币未受影响',
                source_url: 'https:\/\/m.theblockbeats.info\/flash\/301914',
                source_type: '31',
                url_original: 'https:\/\/m.theblockbeats.info\/flash\/301914',
                show_time: '2025-07-09T14:45:44Z',
                link_source_name: '律动 BlockBeats',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '律动 BlockBeats',
                  link: 'https:\/\/www.theblockbeats.info\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20354092',
                title: 'GMX发布紧急缓解方案',
                source_url: 'https:\/\/www.techflowpost.com\/newsletter\/detail_91035.html',
                source_type: '85',
                url_original: 'https:\/\/www.techflowpost.com\/newsletter\/detail_91035.html',
                show_time: '2025-07-09T23:01:14Z',
                link_source_name: '深潮',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '深潮',
                  link: 'https:\/\/www.techflow520.com\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/636e71c704f2d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20355206',
                title:
                  '慢雾：GMX遭攻击核心原因系GMX v1全局空头均价可被操控，GLP 价格遭恶意抬高套利',
                source_url: 'https:\/\/m.theblockbeats.info\/flash\/301932',
                source_type: '31',
                url_original: 'https:\/\/m.theblockbeats.info\/flash\/301932',
                show_time: '2025-07-09T16:54:16Z',
                link_source_name: '律动 BlockBeats',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '律动 BlockBeats',
                  link: 'https:\/\/www.theblockbeats.info\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20358618',
                title: 'GMX黑客已将除FRAX以外资产全部兑换为ETH，或代表已拒绝GMX提出的白帽方案',
                source_url: 'https:\/\/m.theblockbeats.info\/flash\/301973',
                source_type: '31',
                url_original: 'https:\/\/m.theblockbeats.info\/flash\/301973',
                show_time: '2025-07-10T01:29:47Z',
                link_source_name: '律动 BlockBeats',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '律动 BlockBeats',
                  link: 'https:\/\/www.theblockbeats.info\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20360355',
                title:
                  '慢雾：GMX被盗系GLP价格被操控，攻击者通过重入创建大额空头头寸操纵全局平均价格',
                source_url: 'https:\/\/m.theblockbeats.info\/flash\/302016',
                source_type: '31',
                url_original: 'https:\/\/m.theblockbeats.info\/flash\/302016',
                show_time: '2025-07-10T05:27:38Z',
                link_source_name: '律动 BlockBeats',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '律动 BlockBeats',
                  link: 'https:\/\/www.theblockbeats.info\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/635017765a36d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
              {
                article_source_id: '20360666',
                title:
                  '慢雾余弦：GMX v1设计缺陷导致了昨晚的被盗事件，攻击者通过重入创建大额空头头寸操纵全局平均价格',
                source_url: 'https:\/\/www.techflowpost.com\/newsletter\/detail_91112.html',
                source_type: '85',
                url_original: 'https:\/\/www.techflowpost.com\/newsletter\/detail_91112.html',
                show_time: '2025-07-10T14:01:25Z',
                link_source_name: '深潮',
                link_source_english_name: null,
                link_source_traditional_name: null,
                link_source: {
                  name: '深潮',
                  link: 'https:\/\/www.techflow520.com\/',
                  logo_url: 'https:\/\/images.chainfeeds.xyz\/image\/636e71c704f2d.png',
                  english_name: null,
                  traditional_name: null,
                },
              },
            ],
          },
          hot: true,
          up: false,
          new: true,
        },
      ],
    },
  };
};
const getSubject = async () => {
  // chainfeeds 原创文章
  fetch('https://api.chainfeeds.xyz/subject/list?page=1&page_size=10', {
    headers: headers,
    referrerPolicy: 'no-referrer',
    body: null,
    method: 'GET',
  });

  const respone = {
    code: 0,
    message: '成功',
    showErr: 0,
    currentTime: 1752143616,
    data: {
      total_count: 6,
      total_page: 1,
      list: [
        {
          uuid: '6c13d15b-9bdd-452d-8821-18961d2bbb08',
          author_name: null,
          title:
            'ChainFeeds Newsletter 每日精选 Web3 深度投研「简报」+ AI 驱动的热点新闻榜单，帮你做出聪明决策',
          abstract:
            '投研早报丨一个世界计算机的十年之痒/加密货币储备经济模型大解构/从公平发行到注意力资本：Web3 AI 市场格局的快速演变',
          show_time: '2025-07-30T02:08:45Z',
          banner: 'https://images.chainfeeds.xyz/image/web3-ai.png',
          substack_slug: 'web3-ai',
          avatar_url: '',
          author_title: '',
          tags: [],
        },
      ],
    },
  };
};

export {};
