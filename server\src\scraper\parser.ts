import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import { ParsedApiFile, ParsedApiInfo, ScraperConfig } from './types';
import { inferSourceFromPath, inferDataTypeFromEndpoint } from './config';
import { logger } from '../utils/logger';

export class ApiFileParser {
  private config: ScraperConfig;

  constructor(config: ScraperConfig) {
    this.config = config;
  }

  /**
   * 解析draft目录下的所有API文件
   */
  async parseAllFiles(): Promise<ParsedApiFile[]> {
    try {
      logger.info('🔍 开始解析draft目录...', {
        directory: this.config.parsing.draft_directory,
      });

      const files = await this.findApiFiles();
      const results: ParsedApiFile[] = [];

      for (const filePath of files) {
        try {
          const parsed = await this.parseFile(filePath);
          if (parsed.apis.length > 0) {
            results.push(parsed);
          }
        } catch (error) {
          logger.warn('⚠️ 解析文件失败:', {
            file: filePath,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      logger.info('✅ 文件解析完成', {
        totalFiles: files.length,
        successfulFiles: results.length,
        totalApis: results.reduce((sum, file) => sum + file.apis.length, 0),
      });

      return results;
    } catch (error) {
      logger.error('❌ 解析draft目录失败:', error);
      throw error;
    }
  }

  /**
   * 查找所有API文件
   */
  private async findApiFiles(): Promise<string[]> {
    const patterns = this.config.parsing.file_extensions.map(ext => 
      path.join(this.config.parsing.draft_directory, `**/*${ext}`)
    );

    const allFiles: string[] = [];
    for (const pattern of patterns) {
      const files = await glob(pattern, {
        ignore: this.config.parsing.exclude_patterns,
      });
      allFiles.push(...files);
    }

    return [...new Set(allFiles)]; // 去重
  }

  /**
   * 解析单个文件
   */
  async parseFile(filePath: string): Promise<ParsedApiFile> {
    const source = inferSourceFromPath(filePath) || 'unknown';
    const result: ParsedApiFile = {
      file_path: filePath,
      source,
      apis: [],
      parse_errors: [],
    };

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const apis = this.extractApisFromContent(content, filePath);
      result.apis = apis;

      logger.debug('📄 文件解析完成', {
        file: path.basename(filePath),
        source,
        apiCount: apis.length,
      });
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      result.parse_errors.push(errorMsg);
      logger.warn('⚠️ 文件解析出错:', {
        file: filePath,
        error: errorMsg,
      });
    }

    return result;
  }

  /**
   * 从文件内容中提取API信息
   */
  private extractApisFromContent(content: string, filePath: string): ParsedApiInfo[] {
    const apis: ParsedApiInfo[] = [];

    try {
      // 查找所有fetch调用
      const fetchMatches = this.findFetchCalls(content);
      
      for (const match of fetchMatches) {
        const api = this.parseFetchCall(match, filePath);
        if (api) {
          apis.push(api);
        }
      }

      // 查找响应示例
      const responseExamples = this.findResponseExamples(content);
      
      // 将响应示例与API匹配
      this.matchResponseExamples(apis, responseExamples);

    } catch (error) {
      logger.warn('⚠️ 提取API信息失败:', {
        file: path.basename(filePath),
        error: error instanceof Error ? error.message : String(error),
      });
    }

    return apis;
  }

  /**
   * 查找fetch调用
   */
  private findFetchCalls(content: string): RegExpMatchArray[] {
    const fetchRegex = /fetch\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}/gs;
    const matches: RegExpMatchArray[] = [];
    let match;

    while ((match = fetchRegex.exec(content)) !== null) {
      matches.push(match);
    }

    return matches;
  }

  /**
   * 解析单个fetch调用
   */
  private parseFetchCall(match: RegExpMatchArray, filePath: string): ParsedApiInfo | null {
    try {
      const url = match[1];
      const optionsStr = match[2];

      // 解析请求选项
      const method = this.extractMethod(optionsStr);
      const headers = this.extractHeaders(optionsStr);
      const body = this.extractBody(optionsStr);

      // 从URL中提取参数
      const { cleanUrl, params } = this.extractUrlParams(url);

      // 推断端点名称
      const endpoint = this.inferEndpointName(cleanUrl, filePath);
      
      // 推断数据类型
      const dataType = inferDataTypeFromEndpoint(endpoint);

      return {
        endpoint,
        method,
        url: cleanUrl,
        headers,
        params,
        body,
        data_type: dataType,
        description: this.extractDescription(match.input || '', match.index || 0),
      };
    } catch (error) {
      logger.warn('⚠️ 解析fetch调用失败:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * 提取HTTP方法
   */
  private extractMethod(optionsStr: string): 'GET' | 'POST' | 'PUT' | 'DELETE' {
    const methodMatch = optionsStr.match(/['"`]?method['"`]?\s*:\s*['"`](\w+)['"`]/i);
    return (methodMatch?.[1]?.toUpperCase() as any) || 'GET';
  }

  /**
   * 提取请求头
   */
  private extractHeaders(optionsStr: string): Record<string, string> {
    const headers: Record<string, string> = {};
    
    try {
      const headersMatch = optionsStr.match(/['"`]?headers['"`]?\s*:\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}/s);
      if (headersMatch) {
        const headersStr = headersMatch[1];
        const headerRegex = /['"`]?([^'"`:\s]+)['"`]?\s*:\s*['"`]([^'"`]+)['"`]/g;
        let headerMatch;

        while ((headerMatch = headerRegex.exec(headersStr)) !== null) {
          headers[headerMatch[1]] = headerMatch[2];
        }
      }
    } catch (error) {
      logger.debug('提取请求头失败:', error);
    }

    return headers;
  }

  /**
   * 提取请求体
   */
  private extractBody(optionsStr: string): Record<string, any> | string | null {
    try {
      const bodyMatch = optionsStr.match(/['"`]?body['"`]?\s*:\s*(.+?)(?:,\s*['"`]?\w+['"`]?\s*:|$)/s);
      if (!bodyMatch) return null;

      const bodyStr = bodyMatch[1].trim();
      
      if (bodyStr === 'null' || bodyStr === 'undefined') {
        return null;
      }

      // 尝试解析JSON字符串
      if (bodyStr.startsWith('"') && bodyStr.endsWith('"')) {
        try {
          const jsonStr = bodyStr.slice(1, -1).replace(/\\"/g, '"');
          return JSON.parse(jsonStr);
        } catch {
          return bodyStr.slice(1, -1); // 返回字符串内容
        }
      }

      // 尝试解析对象字面量
      if (bodyStr.startsWith('{') && bodyStr.endsWith('}')) {
        try {
          // 简单的对象解析（不完美，但对大多数情况有效）
          const cleanStr = bodyStr.replace(/(['"`])?([a-zA-Z_$][a-zA-Z0-9_$]*)\1?\s*:/g, '"$2":');
          return JSON.parse(cleanStr);
        } catch {
          return bodyStr;
        }
      }

      return bodyStr;
    } catch (error) {
      logger.debug('提取请求体失败:', error);
      return null;
    }
  }

  /**
   * 从URL中提取参数
   */
  private extractUrlParams(url: string): { cleanUrl: string; params: Record<string, any> } {
    try {
      const urlObj = new URL(url);
      const params: Record<string, any> = {};
      
      urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
      });

      return {
        cleanUrl: `${urlObj.origin}${urlObj.pathname}`,
        params: Object.keys(params).length > 0 ? params : undefined,
      };
    } catch {
      return { cleanUrl: url, params: undefined };
    }
  }

  /**
   * 推断端点名称
   */
  private inferEndpointName(url: string, filePath: string): string {
    try {
      // 从文件名推断
      const fileName = path.basename(filePath, path.extname(filePath));
      const parts = fileName.split('.');
      if (parts.length > 1) {
        return parts[parts.length - 1];
      }

      // 从URL路径推断
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/').filter(Boolean);
      if (pathParts.length > 0) {
        return pathParts[pathParts.length - 1];
      }

      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  /**
   * 提取描述信息
   */
  private extractDescription(content: string, index: number): string | undefined {
    try {
      // 查找fetch调用前的注释
      const beforeContent = content.substring(Math.max(0, index - 200), index);
      const commentMatch = beforeContent.match(/\/\/\s*(.+)$/m);
      if (commentMatch) {
        return commentMatch[1].trim();
      }

      // 查找多行注释
      const multiCommentMatch = beforeContent.match(/\/\*\s*(.+?)\s*\*\//s);
      if (multiCommentMatch) {
        return multiCommentMatch[1].trim();
      }

      return undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * 查找响应示例
   */
  private findResponseExamples(content: string): any[] {
    const examples: any[] = [];
    
    try {
      // 查找response变量定义
      const responseRegex = /(?:const|let|var)\s+response\s*=\s*(\{[\s\S]*?\});/g;
      let match;

      while ((match = responseRegex.exec(content)) !== null) {
        try {
          // 尝试解析响应对象
          const responseStr = match[1];
          // 简单的对象解析
          const cleanStr = responseStr
            .replace(/(['"`])?([a-zA-Z_$][a-zA-Z0-9_$]*)\1?\s*:/g, '"$2":')
            .replace(/,\s*}/g, '}')
            .replace(/,\s*]/g, ']');
          
          const parsed = JSON.parse(cleanStr);
          examples.push(parsed);
        } catch (error) {
          logger.debug('解析响应示例失败:', error);
        }
      }
    } catch (error) {
      logger.debug('查找响应示例失败:', error);
    }

    return examples;
  }

  /**
   * 将响应示例与API匹配
   */
  private matchResponseExamples(apis: ParsedApiInfo[], examples: any[]): void {
    // 简单策略：按顺序匹配
    for (let i = 0; i < Math.min(apis.length, examples.length); i++) {
      apis[i].response_example = examples[i];
    }
  }
}
