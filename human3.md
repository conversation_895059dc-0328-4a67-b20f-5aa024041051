chainMix 是一个基于 React Native + Expo 技术栈开发的跨平台移动应用，融合AI智能分析，为用户提供最新的区块链和加密货币资讯。当前目录就是项目工程目录。
我有个后端服务在 @server 是基于 Node.js 聚合几个 API 作为提供服务的 API。
请帮我阅读前后端代码，然后帮我解决以下问题：
1. 核对前端页面和后端 API，查看前端页面模块是否合理。比如：资讯、快讯、文章、推特帖子、AI助手、发现等页面，是否合理、是否足够？查看后端 API 是否有对应实现。
2. 对接前后端 API，核对字段，请求方法、端口等。
3. 检查前后端字段是否合理，字段是否对齐。
4. 增加、或者修复你认为 P0 级别的任务。


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
0. 请阅读前后端源代码。
1. 我已经启动了前后端的服务。
2. 目前我已经有一版初步的前端UI，后端API代码。
3. 目前正在对接前后端API中  
    3.1 请帮我继续对接前后端API，包括但不局限于字段、接口、请求方法、端口等。
    3.2 请帮我实现所有涉及的细节。
4. 请查看页面 http://localhost:8081/ 并分析。  
    4.1 查看页面对接是否正确，还缺少什么功能？
    4.2 页面UI可以如何改善？
    4.3 是否缺少某些模块？
    4.4 是否缺少某些后端实现？
    4.5 是否缺少某些字段？
    4.6 尽可能每个路由都浏览，查看是否有上述问题？


----------------------------------------------------------------------------------

通过pnpm dev:web 可以在本地启动web服务，通过cd server && pnpm dev 可以启动后端API服务。你可以通过工具 playwright 查看页面。
以下需求特别针对前端页面的。
请查看页面 http://localhost:8081/ 并分析。

1. 访问每个路由页面并截图分析。  
2. 查看每个页面实现是否正确，合理，是否缺少某些字段？
3. 页面布局是否合理，如何改善？
4. 是否缺少某些模块？是否缺少某些后端实现？
5. 尽可能每个路由都浏览并截图，查看是否有上述问题。


----------------------------------------------------------------------------------

1. 我并不需要 NetInfo 和 useNetworkStatus 这种逻辑，因为没有这么强的让用户知道网络是否连接的需求，我只需要请求失败的时候，有个小tips提示。请帮我移除 useNetworkStatus  相关逻辑，增加tips相关逻辑。

2. 页面主要场景是移动端，请resize到移动端大小调试。

3. 由于有SPA逻辑，expo动态更新做的并没有太好。修改页面后，请刷新页面查看是否正常。

4. 请继续帮我优化 各个 页面的布局问题，排版问题。


----------------------------------------------------------------------------------
1. 主页推特观点模块卡片大小好像不一致，帮我设置一致宽高，内容显示不完整的用省略号替代。点击 查看全部 按钮进入 推特观点 列表页，列表页可以不限制高度，展示推特全文（默认一致高度，如果有额外内容，可以点击切换显示全部）。
2. 首页的热门资讯模块再优化下排版
3. 首页好像还有很多地方没有接入真实的接口，请列出详细需要的接口和字段
4. 快讯页面似乎右边内容被遮挡了，合理优化排版
5. 首页，快讯、发现页面应该都支持下拉刷新
6. 所有页面都是移动端展示
7. 请帮我修复上述所有问题。

----------------------------------------------------------------------------------

1. handleTwitterPostPress 点击推特帖子，有省略号的帖子，仍然没有展开/省略 切换
2. 首页 推特观点 模块，帖子内容展示有截断
3. 首页 热门资讯模块 热门资讯列表展示太难看了，请优化，可以排版紧凑点
4. 快讯页面仍然右边有截断。
5. 请以移动端视角查看具体前端页面分析，然后修复上述问题。


----------------------------------------------------------------------------------

1. 帮我写一个启动mongodb docker的脚本 scripts/mongo.sh
2. 帮我阅读分析：@server/src/draft 目录所有文件
2.1 文件包含了多个API，包括请求header，url，params，body以及返回示例。
2.2 请你帮我写一个相对独立的逻辑，递归抓取这些API数据保存到对应的表中，不同的API甚至可以使用不同数据库，阅读源码来决定，使用mongodb
2.3 再根据保存的数据，提供API服务。


----------------------------------------------------------------------------------

