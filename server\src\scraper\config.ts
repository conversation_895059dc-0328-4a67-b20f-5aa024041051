import { ScraperConfig } from './types';
import path from 'path';

// ========================================
// 默认配置
// ========================================

export const DEFAULT_SCRAPER_CONFIG: ScraperConfig = {
  mongodb: {
    uri: process.env.MONGODB_URI || '****************************************************************************************',
    database: process.env.MONGODB_DATABASE || 'chainmix',
    collections: {
      api_definitions: 'api_definitions',
      scraped_data: 'scraped_data',
    },
  },
  scraping: {
    concurrent_limit: parseInt(process.env.SCRAPER_CONCURRENT_LIMIT || '5', 10),
    retry_attempts: parseInt(process.env.SCRAPER_RETRY_ATTEMPTS || '3', 10),
    retry_delay: parseInt(process.env.SCRAPER_RETRY_DELAY || '1000', 10),
    timeout: parseInt(process.env.SCRAPER_TIMEOUT || '15000', 10),
    rate_limit: {
      requests_per_minute: parseInt(process.env.SCRAPER_RATE_LIMIT || '60', 10),
      per_source: process.env.SCRAPER_RATE_LIMIT_PER_SOURCE === 'true',
    },
  },
  parsing: {
    draft_directory: path.resolve(__dirname, '../draft'),
    file_extensions: ['.ts', '.js'],
    exclude_patterns: [
      '*.test.*',
      '*.spec.*',
      '*.d.ts',
      'node_modules/**',
      'dist/**',
      'build/**',
    ],
  },
};

// ========================================
// 数据源映射配置
// ========================================

export const SOURCE_MAPPING = {
  // Odaily相关文件
  'odaily': {
    name: 'Odaily星球日报',
    base_url: 'https://web-api.odaily.news',
    file_patterns: ['odaily/**/*.ts', 'odaily.*.ts'],
    endpoints: {
      flash: '快讯',
      post: '文章',
      depth: '深度',
      topic: '话题',
      tweet: '观点',
      price: '价格',
    },
  },
  
  // ChainCatcher相关文件
  'chaincatcher': {
    name: 'ChainCatcher',
    base_url: 'https://www.chaincatcher.com',
    file_patterns: ['chaincatcher.*.ts'],
    endpoints: {
      article: '文章',
      flash: '快讯',
    },
  },
  
  // ForesightNews相关文件
  'foresightnews': {
    name: 'Foresight News',
    base_url: 'https://api.foresightnews.pro',
    file_patterns: ['foresightnews/**/*.ts', 'foresightnews.*.ts'],
    endpoints: {
      dayNews: '每日快讯',
      news: '新闻',
      feed: '订阅源',
      event: '事件',
      topic: '专题',
      column: '专栏',
      article: '文章',
      tools: '工具集',
      fundraising: '融资信息',
      calendars: '日历事件',
    },
  },
  
  // ChainFeeds相关文件
  'chainfeeds': {
    name: 'ChainFeeds',
    base_url: 'https://api.chainfeeds.xyz',
    file_patterns: ['chainfeeds.*.ts'],
    endpoints: {
      feed: '资讯',
      flash: '快讯',
      subject: '原创文章',
    },
  },
  
  // Followin相关文件
  'followin': {
    name: 'Followin',
    base_url: 'https://api.followin.io',
    file_patterns: ['followin.*.ts'],
    endpoints: {
      flash: '快讯',
      news: '推荐新闻',
    },
  },
  
  // PANewsLab相关文件
  'panewslab': {
    name: 'PANews',
    base_url: 'https://api.panewslab.com',
    file_patterns: ['panewslab.*.ts'],
    endpoints: {
      flash: '快讯',
    },
  },
  
  // TechFlow相关文件
  'techflow': {
    name: 'TechFlow',
    base_url: 'https://www.techflowpost.com',
    file_patterns: ['techflow.*.ts'],
    endpoints: {
      flash: '快讯',
    },
  },
  
  // TheBlockBeats相关文件
  'theblockbeats': {
    name: 'TheBlockBeats',
    base_url: 'https://api.blockbeats.cn',
    file_patterns: ['theblockbeats.*.ts'],
    endpoints: {
      news: '新闻',
      finance: '金融',
    },
  },
  
  // TrendX相关文件
  'trendx': {
    name: 'TrendX',
    base_url: 'https://www.trendx.tech',
    file_patterns: ['trendx.*.ts'],
    endpoints: {
      news: '新闻',
      financing: '融资',
      tweet: '推特',
    },
  },
} as const;

// ========================================
// 数据类型映射
// ========================================

export const DATA_TYPE_MAPPING = {
  // 快讯类
  flash: ['flash', 'newsflash', 'breaking'],
  
  // 新闻文章类
  news: ['news', 'article', 'post'],
  
  // 深度分析类
  depth: ['depth', 'analysis', 'research'],
  
  // 话题专题类
  topic: ['topic', 'subject', 'column'],
  
  // 观点推特类
  tweet: ['tweet', 'viewpoint', 'opinion'],
  
  // 价格数据类
  price: ['price', 'market', 'trading'],
  
  // 融资信息类
  financing: ['financing', 'fundraising', 'investment'],
  
  // 事件日历类
  event: ['event', 'calendar', 'schedule'],
  
  // 工具集合类
  tools: ['tools', 'links', 'resources'],
  
  // 订阅源类
  feed: ['feed', 'subscription', 'recommended'],
} as const;

// ========================================
// 请求头模板
// ========================================

export const DEFAULT_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Accept': 'application/json, text/plain, */*',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache',
  'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"Windows"',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-origin',
};

// ========================================
// 配置创建函数
// ========================================

export function createScraperConfig(overrides?: Partial<ScraperConfig>): ScraperConfig {
  const config = { ...DEFAULT_SCRAPER_CONFIG };
  
  if (overrides) {
    // 深度合并配置
    if (overrides.mongodb) {
      config.mongodb = { ...config.mongodb, ...overrides.mongodb };
      if (overrides.mongodb.collections) {
        config.mongodb.collections = { ...config.mongodb.collections, ...overrides.mongodb.collections };
      }
    }
    
    if (overrides.scraping) {
      config.scraping = { ...config.scraping, ...overrides.scraping };
      if (overrides.scraping.rate_limit) {
        config.scraping.rate_limit = { ...config.scraping.rate_limit, ...overrides.scraping.rate_limit };
      }
    }
    
    if (overrides.parsing) {
      config.parsing = { ...config.parsing, ...overrides.parsing };
    }
  }
  
  return config;
}

// ========================================
// 工具函数
// ========================================

/**
 * 根据文件路径推断数据源
 */
export function inferSourceFromPath(filePath: string): string | null {
  const fileName = path.basename(filePath).toLowerCase();
  
  for (const [source, config] of Object.entries(SOURCE_MAPPING)) {
    if (fileName.includes(source)) {
      return source;
    }
  }
  
  return null;
}

/**
 * 根据端点名称推断数据类型
 */
export function inferDataTypeFromEndpoint(endpoint: string): string {
  const lowerEndpoint = endpoint.toLowerCase();
  
  for (const [dataType, keywords] of Object.entries(DATA_TYPE_MAPPING)) {
    if (keywords.some(keyword => lowerEndpoint.includes(keyword))) {
      return dataType;
    }
  }
  
  return 'unknown';
}

/**
 * 获取数据源配置
 */
export function getSourceConfig(source: string) {
  return SOURCE_MAPPING[source as keyof typeof SOURCE_MAPPING] || null;
}

/**
 * 验证配置
 */
export function validateConfig(config: ScraperConfig): boolean {
  try {
    // 基本验证
    if (!config.mongodb.uri || !config.mongodb.database) {
      return false;
    }
    
    if (config.scraping.concurrent_limit <= 0 || config.scraping.concurrent_limit > 20) {
      return false;
    }
    
    if (!config.parsing.draft_directory) {
      return false;
    }
    
    return true;
  } catch (error) {
    return false;
  }
}
